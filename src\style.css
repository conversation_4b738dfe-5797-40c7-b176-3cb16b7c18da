/* 谪仙风流录 - 古风主题样式 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;700&display=swap');

:root {
  --primary-color: #8B4513;
  --secondary-color: #DAA520;
  --accent-color: #CD853F;
  --text-color: #2F1B14;
  --bg-color: #FDF5E6;
  --overlay-bg: rgba(0, 0, 0, 0.7);
  --border-color: #D2B48C;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Serif SC', serif;
  overflow: hidden;
  background: var(--bg-color);
  color: var(--text-color);
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 加载界面 */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 1s ease-out;
}

.loading-content {
  text-align: center;
  color: var(--secondary-color);
}

.loading-content h1 {
  font-size: 3rem;
  margin-bottom: 2rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(218, 165, 32, 0.3);
  border-top: 3px solid var(--secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  font-size: 1.2rem;
  opacity: 0.8;
}

/* Three.js 画布 */
#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  cursor: grab;
}

#three-canvas:active {
  cursor: grabbing;
}

/* UI 覆盖层 */
#ui-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

/* 准星 */
#crosshair {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

#crosshair::before,
#crosshair::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
}

#crosshair::before {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 8px;
}

#crosshair::after {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 2px;
}

/* 交互提示 */
#interaction-prompt {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--overlay-bg);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  border: 2px solid var(--secondary-color);
  font-size: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(5px);
}

#interaction-prompt.visible {
  opacity: 1;
}

/* 控制信息 */
#controls-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: var(--overlay-bg);
  color: white;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
  font-size: 0.9rem;
  backdrop-filter: blur(5px);
}

#controls-info p {
  margin: 0;
  opacity: 0.8;
}

/* 隐藏加载界面的类 */
.loading-hidden {
  opacity: 0;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content h1 {
    font-size: 2rem;
  }

  #controls-info {
    font-size: 0.8rem;
    padding: 10px;
  }
}
